// Login page for admin staff authentication
// Implements secure login with validation and error handling

import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useNavigate } from '@tanstack/react-router'
import { FiMail, FiLock, FiEye, FiEyeOff } from 'react-icons/fi'
import { useAuth, useLogin } from '../../hooks/use-auth'
import { Input } from '../../components/ui/Input'
import { Button } from '../../components/ui/Button'
import styles from './LoginPage.module.scss'

// Login form validation schema
const loginSchema = z.object({
  username: z
    .string()
    .min(1, 'Username is required')
    .email('Please enter a valid email address'),
  password: z
    .string()
    .min(1, 'Password is required')
  // .min(6, 'Password must be at least 6 characters'),
})

type LoginFormData = z.infer<typeof loginSchema>

export const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const { isAuthenticated } = useAuth()
  const loginMutation = useLogin()
  const [showPassword, setShowPassword] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  })

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate({ to: '/' })
    }
  }, [isAuthenticated, navigate])

  const onSubmit = async (data: LoginFormData) => {
    try {
      await loginMutation.mutateAsync(data)
      // Navigation will be handled by the auth state change
    } catch (error: any) {
      // Handle specific error cases
      if (error.message.includes('username') || error.message.includes('email')) {
        setError('username', { message: error.message })
      } else if (error.message.includes('password')) {
        setError('password', { message: error.message })
      } else {
        setError('root', { message: error.message })
      }
    }
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  return (
    <div className={styles.loginContainer}>
      <div className={styles.loginCard}>
        <div className={styles.header}>
          <h1 className={styles.title}>Admin Arena</h1>
          <p className={styles.subtitle}>Sign in to your admin account</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className={styles.form} noValidate>
          {errors.root && (
            <div className={styles.errorAlert} role="alert">
              {errors.root.message}
            </div>
          )}

          <div className={styles.formGroup}>
            <Input
              {...register('username')}
              type="email"
              label="Email Address"
              placeholder="Enter your email"
              leftIcon={<FiMail />}
              error={errors.username?.message}
              autoComplete="email"
              autoFocus
              data-testid="username-input"
            />
          </div>

          <div className={styles.formGroup}>
            <Input
              {...register('password')}
              type={showPassword ? 'text' : 'password'}
              label="Password"
              placeholder="Enter your password"
              leftIcon={<FiLock />}
              rightIcon={
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className={styles.passwordToggle}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </button>
              }
              error={errors.password?.message}
              autoComplete="current-password"
              data-testid="password-input"
            />
          </div>

          <Button
            type="submit"
            variant="primary"
            size="lg"
            fullWidth
            loading={isSubmitting || loginMutation.isPending}
            disabled={isSubmitting || loginMutation.isPending}
            data-testid="login-button"
          >
            {isSubmitting || loginMutation.isPending ? 'Signing in...' : 'Sign In'}
          </Button>
        </form>

        <div className={styles.footer}>
          <p className={styles.footerText}>
            Forgot your password?{' '}
            <button
              type="button"
              className={styles.link}
              onClick={() => {
                // TODO: Implement password reset
                console.log('Password reset not implemented yet')
              }}
            >
              Reset it here
            </button>
          </p>
        </div>
      </div>

      <div className={styles.background}>
        <div className={styles.backgroundPattern}></div>
      </div>
    </div>
  )
}

export default LoginPage
