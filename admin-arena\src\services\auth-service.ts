// Authentication service for staff login and user management
// Handles JWT tokens, permissions, and user session management

import { apiClient } from './api-client'
import { AuthStorage } from '../utils/auth-storage'
import {
  LoginCredentials,
  LoginResponse,
  CurrentUserResponse,
  StaffUser
} from '../types/api-types'

export class AuthService {
  /**
   * Authenticate staff user with username and password
   */
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>('/auth/users/login/', credentials)

      // Store tokens in secure cookies
      AuthStorage.setTokens({
        access: response.data.access,
        refresh: response.data.refresh
      })

      return response.data
    } catch (error: any) {
      // Handle specific login errors
      if (error.response?.status === 401) {
        throw new Error('Invalid email or password')
      } else if (error.response?.status === 403) {
        throw new Error('Account is not authorized for admin access')
      } else if (error.response?.status === 429) {
        throw new Error('Too many login attempts. Please try again later')
      } else {
        throw new Error('<PERSON><PERSON> failed. Please try again')
      }
    }
  }

  /**
   * Get current authenticated user with permissions and groups
   */
  static async getCurrentUser(): Promise<CurrentUserResponse> {
    try {
      const response = await apiClient.get<CurrentUserResponse>('/api/staff/auth/user/')
      return response.data
    } catch (error: any) {
      if (error.response?.status === 401) {
        throw new Error('Authentication required')
      }
      throw new Error('Failed to fetch user information')
    }
  }

  /**
   * Check if user has specific permission
   */
  static async checkPermission(permission: string): Promise<boolean> {
    try {
      const response = await apiClient.post<{ has_permission: boolean }>('/api/staff/auth/check-permission/', {
        permission
      })
      return response.data.has_permission
    } catch (error) {
      console.error('Permission check failed:', error)
      return false
    }
  }

  /**
   * Get all permissions for current user
   */
  static async getUserPermissions(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ permissions: string[] }>('/api/staff/auth/permissions/')
      return response.data.permissions
    } catch (error) {
      console.error('Failed to fetch permissions:', error)
      return []
    }
  }

  /**
   * Get all groups for current user
   */
  static async getUserGroups(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ groups: string[] }>('/api/staff/auth/groups/')
      return response.data.groups
    } catch (error) {
      console.error('Failed to fetch groups:', error)
      return []
    }
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshToken(): Promise<string> {
    const refreshToken = AuthStorage.getRefreshToken()

    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await apiClient.post<{ access: string }>('/api/staff/auth/token/refresh/', {
        refresh: refreshToken
      })

      // Update access token in storage
      AuthStorage.updateAccessToken(response.data.access)

      return response.data.access
    } catch (error: any) {
      // If refresh fails, clear all tokens
      AuthStorage.clearTokens()

      if (error.response?.status === 401) {
        throw new Error('Session expired. Please login again')
      }
      throw new Error('Failed to refresh authentication')
    }
  }

  /**
   * Logout user and clear tokens
   */
  static async logout(): Promise<void> {
    try {
      const refreshToken = AuthStorage.getRefreshToken()

      // Attempt to blacklist the refresh token on server
      if (refreshToken) {
        await apiClient.post('/api/staff/auth/logout/', {
          refresh: refreshToken
        })
      }
    } catch (error) {
      // Even if server logout fails, we should clear local tokens
      console.warn('Server logout failed, clearing local tokens:', error)
    } finally {
      // Always clear local tokens
      AuthStorage.clearTokens()
    }
  }

  /**
   * Check if user is currently authenticated
   */
  static isAuthenticated(): boolean {
    return AuthStorage.hasValidTokens()
  }

  /**
   * Get basic user info from stored token (for UI purposes only)
   */
  static getUserFromToken(): { id: number; email: string; is_superuser: boolean } | null {
    return AuthStorage.getUserFromToken()
  }

  /**
   * Check if access token is expired
   */
  static isTokenExpired(): boolean {
    return AuthStorage.isAccessTokenExpired()
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(): Date | null {
    return AuthStorage.getTokenExpiration()
  }

  /**
   * Update user profile information
   */
  static async updateProfile(data: Partial<StaffUser>): Promise<StaffUser> {
    try {
      const response = await apiClient.patch<StaffUser>('/api/staff/auth/profile/', data)
      return response.data
    } catch (error: any) {
      if (error.response?.status === 400) {
        throw new Error('Invalid profile data provided')
      }
      throw new Error('Failed to update profile')
    }
  }

  /**
   * Change user password
   */
  static async changePassword(data: {
    current_password: string
    new_password: string
    confirm_password: string
  }): Promise<void> {
    try {
      await apiClient.post('/api/staff/auth/change-password/', data)
    } catch (error: any) {
      if (error.response?.status === 400) {
        const errorData = error.response.data
        if (errorData.current_password) {
          throw new Error('Current password is incorrect')
        } else if (errorData.new_password) {
          throw new Error('New password does not meet requirements')
        } else if (errorData.confirm_password) {
          throw new Error('Password confirmation does not match')
        }
        throw new Error('Invalid password data provided')
      }
      throw new Error('Failed to change password')
    }
  }

  /**
   * Request password reset (for admin users)
   */
  static async requestPasswordReset(email: string): Promise<void> {
    try {
      await apiClient.post('/api/staff/auth/password-reset/', { email })
    } catch (error: any) {
      if (error.response?.status === 404) {
        throw new Error('No staff account found with this email address')
      }
      throw new Error('Failed to send password reset email')
    }
  }

  /**
   * Verify password reset token
   */
  static async verifyResetToken(token: string): Promise<boolean> {
    try {
      await apiClient.post('/api/staff/auth/password-reset/verify/', { token })
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Reset password with token
   */
  static async resetPassword(data: {
    token: string
    new_password: string
    confirm_password: string
  }): Promise<void> {
    try {
      await apiClient.post('/api/staff/auth/password-reset/confirm/', data)
    } catch (error: any) {
      if (error.response?.status === 400) {
        const errorData = error.response.data
        if (errorData.token) {
          throw new Error('Invalid or expired reset token')
        } else if (errorData.new_password) {
          throw new Error('New password does not meet requirements')
        } else if (errorData.confirm_password) {
          throw new Error('Password confirmation does not match')
        }
        throw new Error('Invalid reset data provided')
      }
      throw new Error('Failed to reset password')
    }
  }
}
