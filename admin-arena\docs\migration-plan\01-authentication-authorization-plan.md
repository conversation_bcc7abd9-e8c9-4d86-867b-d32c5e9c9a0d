# Authentication & Authorization Technical Plan

## Overview

This document outlines the detailed implementation plan for the authentication and authorization system in the Admin Arena dashboard, based on the existing RBAC system implemented in the Django backend.

## Package Versions

```json
{
  "@tanstack/react-router": "^1.121.34",
  "@tanstack/react-query": "^5.35.1",
  "@hookform/resolvers": "^3.4.2",
  "axios": "^1.10.0",
  "js-cookie": "^3.0.5",
  "react-hook-form": "^7.60.0",
  "zod": "^4.0.0",
  "zustand": "^5.0.6"
}
```

## RBAC System Analysis

### Staff Groups Hierarchy

Based on the backend `STAFF_GROUPS` constants:

```typescript
interface StaffGroups {
  // System Administration
  SUPER_ADMIN: 'Super Administrator (SA)';
  
  // Staff Management Roles
  STAFF_MANAGER: 'Staff Manager (SM)';
  DEPARTMENT_HEAD: 'Department Head (DH)';
  HR_ADMINISTRATOR: 'HR Administrator (HRA)';
  
  // Product Management
  PRODUCT_MANAGER: 'Product Management Executive (PME)';
  PRODUCT_TEAM_MEMBER: 'Product Management Group Member (PMGM)';
  PRODUCT_VIEWER: 'Product Catalog Viewer (PCV)';
  INVENTORY_MANAGER: 'Inventory Management Executive (IME)';
  
  // Order Management
  ORDER_MANAGER: 'Order Management Executive (OME)';
  ORDER_TEAM_MEMBER: 'Order Management Group Member (OMGM)';
  ORDER_FULFILLMENT: 'Order Fulfillment Specialist (OFS)';
  
  // Customer Management
  CUSTOMER_MANAGER: 'Customer Management Executive (CME)';
  CUSTOMER_SERVICE: 'Customer Service Representative (CSR)';
  CUSTOMER_ANALYST: 'Customer Data Analyst (CDA)';
  
  // Content Management
  CONTENT_MANAGER: 'Content Management Executive (CME)';
  CONTENT_MODERATOR: 'Content Moderator (CM)';
  
  // Finance & Analytics
  FINANCE_MANAGER: 'Finance Manager (FM)';
  BUSINESS_ANALYST: 'Business Intelligence Analyst (BIA)';
}
```

### Permission Structure

```typescript
interface Permission {
  id: number;
  name: string;
  codename: string;
  app_label: string;
  model: string;
}

interface StaffUser {
  id: number;
  email: string;
  is_staff: boolean;
  is_superuser: boolean;
  is_active: boolean;
  staff_profile?: StaffProfile;
  groups: string[];
  permissions: string[];
}

interface StaffProfile {
  id: number;
  employee_id: string;
  department: 'PRODUCT' | 'ORDER' | 'CUSTOMER' | 'CONTENT' | 'FINANCE' | 'ADMIN' | 'IT';
  position_title: string;
  manager?: StaffProfile;
  hire_date: string;
  status: 'ACTIVE' | 'INACTIVE' | 'ON_LEAVE' | 'TERMINATED';
  full_name: string;
  is_manager: boolean;
  team_size: number;
}
```

## Authentication Implementation

### 1. Cookie-Based JWT Storage

```typescript
// src/utils/auth-storage.ts
import Cookies from 'js-cookie';

interface AuthTokens {
  access: string;
  refresh: string;
}

export class AuthStorage {
  private static ACCESS_TOKEN_KEY = 'admin_access_token';
  private static REFRESH_TOKEN_KEY = 'admin_refresh_token';
  
  static setTokens(tokens: AuthTokens): void {
    // Set access token with shorter expiry (15 minutes)
    Cookies.set(this.ACCESS_TOKEN_KEY, tokens.access, {
      expires: 1/96, // 15 minutes
      secure: import.meta.env.PROD,
      sameSite: 'strict',
      path: '/'
    });
    
    // Set refresh token with longer expiry (7 days)
    Cookies.set(this.REFRESH_TOKEN_KEY, tokens.refresh, {
      expires: 7,
      secure: import.meta.env.PROD,
      sameSite: 'strict',
      path: '/'
    });
  }
  
  static getAccessToken(): string | null {
    return Cookies.get(this.ACCESS_TOKEN_KEY) || null;
  }
  
  static getRefreshToken(): string | null {
    return Cookies.get(this.REFRESH_TOKEN_KEY) || null;
  }
  
  static clearTokens(): void {
    Cookies.remove(this.ACCESS_TOKEN_KEY, { path: '/' });
    Cookies.remove(this.REFRESH_TOKEN_KEY, { path: '/' });
  }
  
  static hasValidTokens(): boolean {
    return !!(this.getAccessToken() && this.getRefreshToken());
  }
}
```

### 2. API Client with Token Management

```typescript
// src/services/api-client.ts
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { AuthStorage } from '../utils/auth-storage';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = AuthStorage.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = AuthStorage.getRefreshToken();
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }
        
        const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
          refresh: refreshToken
        });
        
        const { access } = response.data;
        AuthStorage.setTokens({ access, refresh: refreshToken });
        
        // Retry original request with new token
        if (originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${access}`;
        }
        
        return apiClient(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        AuthStorage.clearTokens();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

export default apiClient;
```

### 3. Authentication Service

```typescript
// src/services/auth-service.ts
import { apiClient } from './api-client';
import { AuthStorage } from '../utils/auth-storage';

interface LoginCredentials {
  email: string;
  password: string;
}

interface LoginResponse {
  access: string;
  refresh: string;
  user: StaffUser;
}

interface CurrentUserResponse {
  user: StaffUser;
  permissions: string[];
  groups: string[];
}

export class AuthService {
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/staff/auth/login/', credentials);
    
    // Store tokens in cookies
    AuthStorage.setTokens({
      access: response.data.access,
      refresh: response.data.refresh
    });
    
    return response.data;
  }
  
  static async getCurrentUser(): Promise<CurrentUserResponse> {
    const response = await apiClient.get<CurrentUserResponse>('/staff/auth/user/');
    return response.data;
  }
  
  static async checkPermission(permission: string): Promise<boolean> {
    try {
      const response = await apiClient.post<{ has_permission: boolean }>('/staff/auth/check-permission/', {
        permission
      });
      return response.data.has_permission;
    } catch {
      return false;
    }
  }
  
  static async getUserPermissions(): Promise<string[]> {
    const response = await apiClient.get<{ permissions: string[] }>('/staff/auth/permissions/');
    return response.data.permissions;
  }
  
  static async logout(): Promise<void> {
    try {
      const refreshToken = AuthStorage.getRefreshToken();
      if (refreshToken) {
        await apiClient.post('/staff/auth/logout/', { refresh: refreshToken });
      }
    } finally {
      AuthStorage.clearTokens();
    }
  }
  
  static isAuthenticated(): boolean {
    return AuthStorage.hasValidTokens();
  }
}
```

### 4. Zustand Auth Store

```typescript
// src/stores/auth-store.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AuthService } from '../services/auth-service';

interface AuthState {
  user: StaffUser | null;
  permissions: string[];
  groups: string[];
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  checkPermission: (permission: string) => boolean;
  hasGroup: (group: string) => boolean;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        user: null,
        permissions: [],
        groups: [],
        isAuthenticated: false,
        isLoading: false,
        error: null,
        
        login: async (credentials) => {
          set({ isLoading: true, error: null });
          try {
            const response = await AuthService.login(credentials);
            const userResponse = await AuthService.getCurrentUser();
            
            set({
              user: response.user,
              permissions: userResponse.permissions,
              groups: userResponse.groups,
              isAuthenticated: true,
              isLoading: false
            });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Login failed',
              isLoading: false,
              isAuthenticated: false
            });
            throw error;
          }
        },
        
        logout: async () => {
          set({ isLoading: true });
          try {
            await AuthService.logout();
          } finally {
            set({
              user: null,
              permissions: [],
              groups: [],
              isAuthenticated: false,
              isLoading: false,
              error: null
            });
          }
        },
        
        getCurrentUser: async () => {
          if (!AuthService.isAuthenticated()) {
            set({ isAuthenticated: false });
            return;
          }
          
          set({ isLoading: true });
          try {
            const response = await AuthService.getCurrentUser();
            set({
              user: response.user,
              permissions: response.permissions,
              groups: response.groups,
              isAuthenticated: true,
              isLoading: false
            });
          } catch {
            set({
              user: null,
              permissions: [],
              groups: [],
              isAuthenticated: false,
              isLoading: false
            });
          }
        },
        
        checkPermission: (permission: string) => {
          const { permissions, user } = get();
          return user?.is_superuser || permissions.includes(permission);
        },
        
        hasGroup: (group: string) => {
          const { groups, user } = get();
          return user?.is_superuser || groups.includes(group);
        },
        
        clearError: () => set({ error: null })
      }),
      {
        name: 'admin-auth-store',
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          permissions: state.permissions,
          groups: state.groups
        })
      }
    )
  )
);
```

## Authorization Components

### 1. Permission Guard Component

```typescript
// src/components/auth/PermissionGuard.tsx
import React from 'react';
import { useAuthStore } from '../../stores/auth-store';

interface PermissionGuardProps {
  permission?: string;
  group?: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  group,
  fallback = null,
  children
}) => {
  const { checkPermission, hasGroup, user } = useAuthStore();
  
  // Superuser has access to everything
  if (user?.is_superuser) {
    return <>{children}</>;
  }
  
  // Check permission if provided
  if (permission && !checkPermission(permission)) {
    return <>{fallback}</>;
  }
  
  // Check group if provided
  if (group && !hasGroup(group)) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
};
```

### 2. Route Protection Hook

```typescript
// src/hooks/use-auth-guard.ts
import { useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useAuthStore } from '../stores/auth-store';

interface UseAuthGuardOptions {
  permission?: string;
  group?: string;
  redirectTo?: string;
}

export const useAuthGuard = (options: UseAuthGuardOptions = {}) => {
  const { permission, group, redirectTo = '/login' } = options;
  const { isAuthenticated, checkPermission, hasGroup, user } = useAuthStore();
  const navigate = useNavigate();
  
  useEffect(() => {
    if (!isAuthenticated) {
      navigate({ to: redirectTo });
      return;
    }
    
    // Superuser bypasses all checks
    if (user?.is_superuser) {
      return;
    }
    
    // Check specific permission
    if (permission && !checkPermission(permission)) {
      navigate({ to: '/unauthorized' });
      return;
    }
    
    // Check group membership
    if (group && !hasGroup(group)) {
      navigate({ to: '/unauthorized' });
      return;
    }
  }, [isAuthenticated, permission, group, checkPermission, hasGroup, user, navigate, redirectTo]);
  
  return {
    isAuthenticated,
    hasPermission: permission ? checkPermission(permission) : true,
    hasGroup: group ? hasGroup(group) : true,
    user
  };
};
```

## TanStack Router Integration

### 1. Route Configuration with Auth

```typescript
// src/routes/__root.tsx
import { createRootRoute, Outlet } from '@tanstack/react-router';
import { TanStackRouterDevtools } from '@tanstack/router-devtools';
import { AuthProvider } from '../components/auth/AuthProvider';

export const Route = createRootRoute({
  component: () => (
    <AuthProvider>
      <Outlet />
      <TanStackRouterDevtools />
    </AuthProvider>
  ),
});
```

### 2. Protected Route Example

```typescript
// src/routes/orders/index.tsx
import { createFileRoute } from '@tanstack/react-router';
import { OrdersPage } from '../../pages/orders/OrdersPage';
import { useAuthGuard } from '../../hooks/use-auth-guard';

export const Route = createFileRoute('/orders/')({
  component: OrdersPageComponent,
});

function OrdersPageComponent() {
  const { isAuthenticated, hasPermission } = useAuthGuard({
    permission: 'staff.view_orderproxy'
  });
  
  if (!isAuthenticated || !hasPermission) {
    return <div>Loading...</div>;
  }
  
  return <OrdersPage />;
}
```

## Next Steps

1. **Install Dependencies**: Add required packages to package.json
2. **Environment Setup**: Configure API base URL and other environment variables
3. **Login Page**: Create login form with validation
4. **Route Guards**: Implement protected routes with permission checks
5. **Navigation**: Build sidebar navigation with role-based visibility
6. **Testing**: Write unit tests for auth components and services
