// Enhanced API client with automatic token refresh and error handling
// Based on react-ts-client patterns with admin-specific enhancements

import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { AuthStorage } from '../utils/auth-storage';
import { PaginatedResponse } from '../types/api-types';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

// Create the main axios instance
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = AuthStorage.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for token refresh and error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };
    
    // Handle 401 errors with token refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = AuthStorage.getRefreshToken();
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }
        
        // Attempt to refresh the token
        const response = await axios.post(`${API_BASE_URL}/staff/auth/token/refresh/`, {
          refresh: refreshToken
        });
        
        const { access } = response.data;
        AuthStorage.updateAccessToken(access);
        
        // Retry original request with new token
        if (originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${access}`;
        }
        
        return apiClient(originalRequest);
      } catch (refreshError) {
        // Refresh failed, clear tokens and redirect to login
        AuthStorage.clearTokens();
        
        // Only redirect if we're not already on the login page
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
        
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

/**
 * Generic API client class for CRUD operations
 * Based on react-ts-client APIClient pattern
 */
class APIClient<TResponse, TRequest = TResponse> {
  endpoint: string;

  constructor(endpoint: string) {
    this.endpoint = endpoint;
  }

  /**
   * GET single resource
   */
  get = async (config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.get<TResponse>(this.endpoint, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * GET paginated list of resources
   */
  getAll = async (config?: AxiosRequestConfig): Promise<PaginatedResponse<TResponse>> => {
    try {
      const response = await apiClient.get<PaginatedResponse<TResponse>>(this.endpoint, config);
      console.log(`API Call: ${response.config.url}`);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * POST create new resource
   */
  post = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.post<TResponse>(this.endpoint, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * PATCH partial update resource
   */
  patch = async (data: Partial<TRequest>, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.patch<TResponse>(this.endpoint, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * PUT full update resource
   */
  put = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.put<TResponse>(this.endpoint, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * DELETE resource
   */
  delete = async (itemId?: number | string): Promise<void> => {
    try {
      const url = itemId ? `${this.endpoint}${itemId}/` : this.endpoint;
      await apiClient.delete(url);
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  };

  /**
   * Handle API errors with proper logging and formatting
   */
  private handleError = (error: unknown): void => {
    if (axios.isAxiosError(error)) {
      // Don't log cancelled requests as errors
      if (error.code === 'ERR_CANCELED' || error.message === 'canceled') {
        throw error;
      }

      // Don't log expected authentication errors to reduce console noise
      const isAuthError = error.response?.status === 401 || error.response?.status === 403;

      if (!isAuthError) {
        console.error('API Error:', error.message);
        if (error.response) {
          console.error('Response data:', error.response.data);
          console.error('Response status:', error.response.status);
        }
      }

      // Format error for consistent handling
      throw {
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data
      };
    }
    
    console.error('Unexpected error:', error);
    throw error;
  };
}

export default APIClient;
