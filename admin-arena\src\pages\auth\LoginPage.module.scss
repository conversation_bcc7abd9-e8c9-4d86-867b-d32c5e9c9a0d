// Login page styles
// Modern, professional design for admin authentication

@use '../../scss/variables' as vars;
@use '../../scss/mixins' as mixins;

.loginContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, vars.$primary-600 0%, vars.$primary-800 100%);
  padding: vars.$spacing-4;
}

.loginCard {
  @include mixins.card;
  width: 100%;
  max-width: 400px;
  padding: vars.$spacing-8;
  position: relative;
  z-index: 1;
  box-shadow: vars.$shadow-2xl;
  
  @include mixins.responsive(sm) {
    padding: vars.$spacing-10;
  }
}

.header {
  text-align: center;
  margin-bottom: vars.$spacing-8;
}

.title {
  font-size: vars.$font-size-3xl;
  font-weight: vars.$font-weight-bold;
  color: vars.$gray-900;
  margin: 0 0 vars.$spacing-2 0;
  line-height: vars.$line-height-tight;
}

.subtitle {
  font-size: vars.$font-size-base;
  color: vars.$gray-600;
  margin: 0;
  line-height: vars.$line-height-normal;
}

.form {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-6;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.errorAlert {
  background-color: vars.$error-50;
  border: 1px solid vars.$error-200;
  border-radius: vars.$border-radius;
  padding: vars.$spacing-3 vars.$spacing-4;
  color: vars.$error-700;
  font-size: vars.$font-size-sm;
  line-height: vars.$line-height-normal;
  
  &::before {
    content: '⚠';
    margin-right: vars.$spacing-2;
  }
}

.passwordToggle {
  background: none;
  border: none;
  cursor: pointer;
  color: vars.$gray-400;
  padding: vars.$spacing-1;
  border-radius: vars.$border-radius-sm;
  transition: vars.$transition-colors;
  
  &:hover {
    color: vars.$gray-600;
    background-color: vars.$gray-100;
  }
  
  &:focus {
    outline: 2px solid vars.$primary-500;
    outline-offset: 2px;
  }
  
  svg {
    width: vars.$spacing-4;
    height: vars.$spacing-4;
  }
}

.footer {
  margin-top: vars.$spacing-6;
  text-align: center;
}

.footerText {
  font-size: vars.$font-size-sm;
  color: vars.$gray-600;
  margin: 0;
  line-height: vars.$line-height-normal;
}

.link {
  background: none;
  border: none;
  color: vars.$primary-600;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  padding: 0;
  
  &:hover {
    color: vars.$primary-700;
  }
  
  &:focus {
    outline: 2px solid vars.$primary-500;
    outline-offset: 2px;
    border-radius: vars.$border-radius-sm;
  }
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 0;
}

.backgroundPattern {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

// Responsive adjustments
@include mixins.responsive(sm) {
  .loginContainer {
    padding: vars.$spacing-6;
  }
  
  .title {
    font-size: vars.$font-size-4xl;
  }
}

// Focus and accessibility improvements
.loginCard {
  &:focus-within {
    box-shadow: vars.$shadow-2xl, 0 0 0 4px rgba(vars.$primary-500, 0.1);
  }
}

// Loading state
.form {
  &:has(button[disabled]) {
    pointer-events: none;
    opacity: 0.8;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .loginCard {
    border: 2px solid vars.$gray-900;
  }
  
  .title {
    color: vars.$gray-900;
  }
  
  .subtitle {
    color: vars.$gray-700;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .backgroundPattern {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}
