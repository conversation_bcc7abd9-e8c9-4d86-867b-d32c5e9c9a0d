// Orders list page styles
// Professional order management interface

@use '../../scss/variables' as vars;
@use '../../scss/mixins' as mixins;

.ordersPage {
  padding: vars.$spacing-6;
  max-width: 1400px;
  margin: 0 auto;
  
  @include mixins.responsive(sm) {
    padding: vars.$spacing-8;
  }
}

.header {
  @include mixins.flex-between;
  margin-bottom: vars.$spacing-6;
  gap: vars.$spacing-4;
  
  @include mixins.mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.headerLeft {
  flex: 1;
}

.title {
  font-size: vars.$font-size-3xl;
  font-weight: vars.$font-weight-bold;
  color: vars.$gray-900;
  margin: 0 0 vars.$spacing-2 0;
  line-height: vars.$line-height-tight;
}

.subtitle {
  font-size: vars.$font-size-base;
  color: vars.$gray-600;
  margin: 0;
  line-height: vars.$line-height-normal;
}

.headerActions {
  @include mixins.flex-start;
  gap: vars.$spacing-3;
  
  @include mixins.mobile-only {
    width: 100%;
    
    button {
      flex: 1;
    }
  }
}

.filters {
  @include mixins.flex-start;
  gap: vars.$spacing-4;
  margin-bottom: vars.$spacing-6;
  padding: vars.$spacing-4;
  background: white;
  border: 1px solid vars.$gray-200;
  border-radius: vars.$border-radius-lg;
  
  @include mixins.mobile-only {
    flex-direction: column;
    gap: vars.$spacing-3;
  }
}

.searchFilter {
  flex: 1;
  min-width: 300px;
  
  @include mixins.mobile-only {
    width: 100%;
    min-width: auto;
  }
}

.statusFilter {
  min-width: 200px;
  
  @include mixins.mobile-only {
    width: 100%;
    min-width: auto;
  }
}

.statusSelect {
  @include mixins.input-base;
  min-width: 100%;
  cursor: pointer;
  
  &:focus {
    border-color: vars.$primary-500;
    box-shadow: 0 0 0 3px rgba(vars.$primary-500, 0.1);
  }
}

.bulkActions {
  @include mixins.flex-start;
  gap: vars.$spacing-2;
  
  @include mixins.mobile-only {
    flex-direction: column;
    width: 100%;
    
    button {
      width: 100%;
    }
  }
}

// Order-specific styles
.orderNumber {
  font-family: vars.$font-family-mono;
  font-weight: vars.$font-weight-semibold;
  color: vars.$primary-600;
}

.customerInfo {
  @include mixins.flex-column;
  gap: vars.$spacing-0-5;
}

.customerName {
  font-weight: vars.$font-weight-medium;
  color: vars.$gray-900;
  font-size: vars.$font-size-sm;
}

.customerEmail {
  font-size: vars.$font-size-xs;
  color: vars.$gray-500;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: vars.$spacing-1 vars.$spacing-2;
  border-radius: vars.$border-radius;
  font-size: vars.$font-size-xs;
  font-weight: vars.$font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  
  &.statusPending {
    background-color: vars.$warning-100;
    color: vars.$warning-700;
    border: 1px solid vars.$warning-200;
  }
  
  &.statusProcessing {
    background-color: vars.$info-100;
    color: vars.$info-700;
    border: 1px solid vars.$info-200;
  }
  
  &.statusShipped {
    background-color: vars.$primary-100;
    color: vars.$primary-700;
    border: 1px solid vars.$primary-200;
  }
  
  &.statusDelivered {
    background-color: vars.$success-100;
    color: vars.$success-700;
    border: 1px solid vars.$success-200;
  }
  
  &.statusCancelled {
    background-color: vars.$error-100;
    color: vars.$error-700;
    border: 1px solid vars.$error-200;
  }
}

// Responsive adjustments
@include mixins.responsive(lg) {
  .filters {
    flex-direction: row;
    align-items: center;
  }
  
  .searchFilter {
    max-width: 400px;
  }
  
  .statusFilter {
    max-width: 200px;
  }
}

// Loading states
.ordersPage {
  &:has(.loadingContainer) {
    .filters {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// Print styles
@media print {
  .header,
  .filters,
  .bulkActions {
    display: none;
  }
  
  .ordersPage {
    padding: 0;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .statusBadge {
    border-width: 2px;
    font-weight: vars.$font-weight-bold;
  }
  
  .orderNumber {
    color: vars.$gray-900;
    text-decoration: underline;
  }
}
