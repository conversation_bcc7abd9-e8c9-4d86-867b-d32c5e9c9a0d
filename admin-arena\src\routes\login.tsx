// Login route for admin authentication
// Public route that redirects authenticated users to dashboard

import { createFileRoute, redirect } from '@tanstack/react-router';
import { LoginPage } from '../pages/auth/LoginPage';
import { AuthService } from '../services/auth-service';

export const Route = createFileRoute('/login')({
  beforeLoad: () => {
    // Redirect to dashboard if already authenticated
    if (AuthService.isAuthenticated()) {
      throw redirect({ to: '/' });
    }
  },
  component: LoginPage,
});
