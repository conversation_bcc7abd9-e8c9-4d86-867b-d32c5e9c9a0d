// Dashboard page styles
// Modern admin dashboard with cards and responsive grid

@use '../../scss/variables' as vars;
@use '../../scss/mixins' as mixins;

.dashboard {
  padding: vars.$spacing-6;
  max-width: 1400px;
  margin: 0 auto;
  
  @include mixins.responsive(sm) {
    padding: vars.$spacing-8;
  }
}

.header {
  @include mixins.flex-between;
  margin-bottom: vars.$spacing-8;
  gap: vars.$spacing-4;
  
  @include mixins.mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.title {
  font-size: vars.$font-size-3xl;
  font-weight: vars.$font-weight-bold;
  color: vars.$gray-900;
  margin: 0 0 vars.$spacing-2 0;
  line-height: vars.$line-height-tight;
  
  @include mixins.responsive(sm) {
    font-size: vars.$font-size-4xl;
  }
}

.subtitle {
  font-size: vars.$font-size-lg;
  color: vars.$gray-600;
  margin: 0;
  line-height: vars.$line-height-normal;
}

.headerActions {
  display: flex;
  gap: vars.$spacing-3;
  
  @include mixins.mobile-only {
    width: 100%;
    
    button {
      flex: 1;
    }
  }
}

// Stats Grid
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: vars.$spacing-6;
  margin-bottom: vars.$spacing-8;
}

.statCard {
  @include mixins.card;
  padding: vars.$spacing-6;
  transition: vars.$transition-shadow;
  
  &:hover {
    box-shadow: vars.$shadow-md;
  }
}

.statHeader {
  @include mixins.flex-between;
  margin-bottom: vars.$spacing-4;
}

.statIcon {
  @include mixins.flex-center;
  width: vars.$spacing-12;
  height: vars.$spacing-12;
  background: linear-gradient(135deg, vars.$primary-500, vars.$primary-600);
  border-radius: vars.$border-radius-lg;
  color: white;
  
  svg {
    width: vars.$spacing-6;
    height: vars.$spacing-6;
  }
}

.statChange {
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-semibold;
  color: vars.$success-600;
  background-color: vars.$success-50;
  padding: vars.$spacing-1 vars.$spacing-2;
  border-radius: vars.$border-radius;
}

.statContent {
  .statValue {
    font-size: vars.$font-size-3xl;
    font-weight: vars.$font-weight-bold;
    color: vars.$gray-900;
    margin: 0 0 vars.$spacing-1 0;
    line-height: vars.$line-height-tight;
  }
  
  .statLabel {
    font-size: vars.$font-size-base;
    color: vars.$gray-600;
    margin: 0 0 vars.$spacing-3 0;
    line-height: vars.$line-height-normal;
  }
}

.statDetails {
  display: flex;
  gap: vars.$spacing-4;
  font-size: vars.$font-size-sm;
  color: vars.$gray-500;
  
  span {
    &:not(:last-child)::after {
      content: '•';
      margin-left: vars.$spacing-2;
      color: vars.$gray-300;
    }
  }
}

// Content Grid
.contentGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: vars.$spacing-8;
  
  @include mixins.mobile-only {
    grid-template-columns: 1fr;
  }
}

.quickActions,
.recentActivity {
  @include mixins.card;
  padding: vars.$spacing-6;
}

.sectionTitle {
  font-size: vars.$font-size-xl;
  font-weight: vars.$font-weight-semibold;
  color: vars.$gray-900;
  margin: 0 0 vars.$spacing-6 0;
  line-height: vars.$line-height-tight;
}

// Quick Actions
.actionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: vars.$spacing-4;
}

// Recent Activity
.activityList {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-4;
}

.activityItem {
  display: flex;
  gap: vars.$spacing-3;
  padding: vars.$spacing-3;
  border-radius: vars.$border-radius;
  transition: vars.$transition-colors;
  
  &:hover {
    background-color: vars.$gray-50;
  }
}

.activityIcon {
  @include mixins.flex-center;
  width: vars.$spacing-8;
  height: vars.$spacing-8;
  background-color: vars.$gray-100;
  border-radius: vars.$border-radius;
  color: vars.$gray-600;
  flex-shrink: 0;
  
  svg {
    width: vars.$spacing-4;
    height: vars.$spacing-4;
  }
}

.activityContent {
  flex: 1;
  min-width: 0;
}

.activityMessage {
  font-size: vars.$font-size-sm;
  color: vars.$gray-900;
  margin: 0 0 vars.$spacing-1 0;
  line-height: vars.$line-height-normal;
  
  @include mixins.truncate;
}

.activityTime {
  @include mixins.flex-start;
  gap: vars.$spacing-1;
  font-size: vars.$font-size-xs;
  color: vars.$gray-500;
  
  svg {
    width: vars.$spacing-3;
    height: vars.$spacing-3;
  }
}

// Responsive adjustments
@include mixins.responsive(lg) {
  .statsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@include mixins.responsive(xl) {
  .dashboard {
    padding: vars.$spacing-10;
  }
  
  .statsGrid {
    gap: vars.$spacing-8;
  }
  
  .contentGrid {
    gap: vars.$spacing-10;
  }
}

// Loading states
.statCard {
  &.loading {
    .statValue {
      background: linear-gradient(90deg, vars.$gray-200 25%, vars.$gray-100 50%, vars.$gray-200 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: vars.$border-radius;
      color: transparent;
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Dark mode support (future enhancement)
@media (prefers-color-scheme: dark) {
  .title {
    color: vars.$gray-100;
  }
  
  .subtitle {
    color: vars.$gray-300;
  }
  
  .statCard {
    background-color: vars.$gray-800;
    border-color: vars.$gray-700;
  }
}
