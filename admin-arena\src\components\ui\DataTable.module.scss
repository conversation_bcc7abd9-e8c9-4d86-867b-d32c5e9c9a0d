// DataTable component styles
// Professional table design with interactive features

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.tableContainer {
  @include card;
  overflow: hidden;
}

.bulkActions {
  @include flex-between;
  padding: $spacing-4;
  background-color: $primary-50;
  border-bottom: 1px solid $primary-200;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.selectedCount {
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  color: vars.$primary-700;
}

.bulkActionsContent {
  @include mixins.flex-start;
  gap: vars.$spacing-2;

  @include mixins.mobile-only {
    width: 100%;

    button {
      flex: 1;
    }
  }
}

.tableWrapper {
  overflow: auto;

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: vars.$gray-100;
  }

  &::-webkit-scrollbar-thumb {
    background: vars.$gray-300;
    border-radius: vars.$border-radius;

    &:hover {
      background: vars.$gray-400;
    }
  }
}

.table {
  @include mixins.table-base;

  &.stickyHeader {
    thead th {
      position: sticky;
      top: 0;
      z-index: vars.$z-10;
      background-color: vars.$gray-50;
      box-shadow: 0 1px 0 vars.$gray-200;
    }
  }
}

.thead {
  background-color: vars.$gray-50;
}

.th {
  font-weight: vars.$font-weight-semibold;
  font-size: vars.$font-size-xs;
  color: vars.$gray-700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: vars.$spacing-3 vars.$spacing-4;
  border-bottom: 1px solid vars.$gray-200;

  &.sortable {
    cursor: pointer;
    user-select: none;
    transition: vars.$transition-colors;

    &:hover {
      background-color: vars.$gray-100;
    }
  }
}

.thContent {
  @include mixins.flex-between;
  gap: vars.$spacing-2;
}

.sortIcon {
  @include mixins.flex-center;
  color: vars.$gray-500;

  svg {
    width: vars.$spacing-3;
    height: vars.$spacing-3;
  }
}

.tbody {
  tr {
    transition: vars.$transition-colors;

    &:hover {
      background-color: vars.$gray-50;
    }

    &.selected {
      background-color: vars.$primary-50;

      &:hover {
        background-color: vars.$primary-100;
      }
    }

    &.clickable {
      cursor: pointer;
    }
  }
}

.tr {
  &.hovered {
    background-color: vars.$gray-50;
  }
}

.td {
  padding: vars.$spacing-3 vars.$spacing-4;
  font-size: vars.$font-size-sm;
  color: vars.$gray-900;
  border-bottom: 1px solid vars.$gray-200;
  vertical-align: middle;

  // Prevent text overflow in cells
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.checkboxColumn {
  width: vars.$spacing-12;
  text-align: center;
  padding: vars.$spacing-3 vars.$spacing-2;
}

.checkbox {
  width: vars.$spacing-4;
  height: vars.$spacing-4;
  border-radius: vars.$border-radius-sm;
  border: 1px solid vars.$gray-300;
  cursor: pointer;

  &:checked {
    background-color: vars.$primary-600;
    border-color: vars.$primary-600;
  }

  &:focus {
    outline: 2px solid vars.$primary-500;
    outline-offset: 2px;
  }
}

.actionsColumn {
  width: vars.$spacing-12;
  text-align: center;
  padding: vars.$spacing-2;
}

.loadingContainer {
  @include mixins.flex-column-center;
  padding: vars.$spacing-16;
  gap: vars.$spacing-4;
  color: vars.$gray-600;
}

.loadingSpinner {
  width: vars.$spacing-8;
  height: vars.$spacing-8;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.emptyState {
  @include mixins.flex-center;
  padding: vars.$spacing-16;
  color: vars.$gray-500;
}

.emptyMessage {
  font-size: vars.$font-size-base;
  margin: 0;
}

// Responsive adjustments
@include mixins.mobile-only {
  .table {
    font-size: vars.$font-size-xs;
  }

  .th,
  .td {
    padding: vars.$spacing-2 vars.$spacing-3;
  }

  .td {
    max-width: 120px;
  }
}

// Print styles
@media print {

  .bulkActions,
  .actionsColumn,
  .checkboxColumn {
    display: none;
  }

  .table {
    box-shadow: none;
    border: 1px solid vars.$gray-300;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .table {
    border: 2px solid vars.$gray-900;
  }

  .th,
  .td {
    border-bottom: 1px solid vars.$gray-900;
  }

  .tr.selected {
    background-color: vars.$primary-200;
    border: 2px solid vars.$primary-600;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {

  .tr,
  .th.sortable,
  .loadingSpinner {
    transition: none;
    animation: none;
  }
}