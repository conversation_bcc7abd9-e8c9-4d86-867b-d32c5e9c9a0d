// Fonts
$primary-font-family: '<PERSON>', '<PERSON> MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;

// Font Size
$font-size-1: 12px;
$font-size-2: 16px;
$font-size-3: 18px;
$font-size-4: 20px;
$font-size-5: 24px;

// Colors
$primary-dark: #131921;

$primary-yellow: #FFD814;
$lighten-yellow: #ffe180;

$primary-red: #cf0707;
$error-red-bg: #FF9494;
$error-red: #f00000;

$primary-blue: #0091cf;
$lighten-blue: #00b3ff;
$sky-light-blue: #a4e4ff;
$sky-lighter-blue: #d4f4ff;

$primary-dark-blue: #232F3E;

$primary-green: #2E9F1C;

$primary-dark-text-color: #333;
$primary-lighter-text-color: #666;

$info-bg: #bedeff;
$info-text: #084298;

$warning-bg: #ffe180;
$warning-text: #534000;

$error-bg: #ffb2b9;
$error-text: #580007;

$success-bg: #9fffa3;
$success-text: #002e02;


// Box shadow
$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;
$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;
$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;

// Border radius
$border-radius-1: 3px;
$border-radius-2: 5px;
$border-radius-3: 8px;
$border-radius-4: 10px;

// Padding
$padding-1: 5px;
$padding-2: 10px;
$padding-3: 12px;
$padding-4: 15px;
$padding-5: 20px;

// Media queries
$mobile: 576px;
$tablet: 768px;
$laptop: 992px;
$monitor: 1200px;

// Additional Admin Arena Variables
// These are needed for the admin interface components

// Grayscale colors for admin interface
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Primary colors for admin interface
$primary-50: #eff6ff;
$primary-100: #dbeafe;
$primary-200: #bfdbfe;
$primary-300: #93c5fd;
$primary-400: #60a5fa;
$primary-500: #3b82f6;
$primary-600: #2563eb;
$primary-700: #1d4ed8;
$primary-800: #1e40af;
$primary-900: #1e3a8a;

// Semantic colors
$success-50: #f0fdf4;
$success-100: #dcfce7;
$success-200: #bbf7d0;
$success-500: #22c55e;
$success-600: #16a34a;
$success-700: #15803d;
$success-800: #166534;

$error-50: #fef2f2;
$error-100: #fee2e2;
$error-200: #fecaca;
$error-500: #ef4444;
$error-600: #dc2626;
$error-700: #b91c1c;
$error-800: #991b1b;

$warning-100: #fef3c7;
$warning-200: #fde68a;
$warning-500: #f59e0b;
$warning-600: #d97706;
$warning-700: #b45309;
$warning-800: #92400e;

$info-100: #e0f2fe;
$info-200: #bae6fd;
$info-500: #0ea5e9;
$info-600: #0284c7;

// Typography
$font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-family-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, monospace;

// Font weights
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Line heights
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.625;

// Font sizes (using existing naming convention)
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;
$font-size-5xl: 48px;
$font-size-6xl: 60px;

// Spacing (using existing naming convention)
$spacing-0-5: 2px;
$spacing-1: 4px;
$spacing-2: 8px;
$spacing-3: 12px;
$spacing-4: 16px;
$spacing-5: 20px;
$spacing-6: 24px;
$spacing-8: 32px;
$spacing-10: 40px;
$spacing-12: 48px;
$spacing-16: 64px;

// Border radius (extending existing)
$border-radius-sm: 2px;
$border-radius: 4px;
$border-radius-lg: 8px;
$border-radius-full: 9999px;

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// Breakpoints (extending existing)
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// Z-index scale
$z-10: 10;
$z-20: 20;
$z-30: 30;
$z-40: 40;
$z-50: 50;
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;

// Transitions
$transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
$transition-colors: color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1);
$transition-shadow: box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1);
$transition-transform: transform 150ms cubic-bezier(0.4, 0, 0.2, 1);

// Admin-specific layout
$sidebar-width: 280px;
$sidebar-collapsed-width: 80px;
$header-height: 64px;